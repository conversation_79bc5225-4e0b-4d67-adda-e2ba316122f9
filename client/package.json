{"name": "habit-tracker-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:with-tokens": "concurrently \"yarn watch-tokens\" \"yarn dev\"", "build": "yarn build-tokens && tsc && vite build", "build-tokens": "node build-tokens.js", "watch-tokens": "node watch-tokens.js", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@radix-ui/themes": "^3.2.1", "axios": "^1.3.4", "clsx": "^2.1.1", "lucide-react": "^0.220.0", "pixi.js": "^7.2.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.43.0", "react-router-dom": "^6.8.0", "tailwind-merge": "^3.3.1", "zustand": "^4.3.6"}, "devDependencies": {"@types/node": "^24.0.10", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@typescript-eslint/eslint-plugin": "^5.57.1", "@typescript-eslint/parser": "^5.57.1", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.14", "concurrently": "^9.2.0", "eslint": "^8.38.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "postcss": "^8.4.21", "style-dictionary": "^3.9.2", "tailwindcss": "^3.3.0", "typescript": "^5.0.2", "vite": "^4.3.2"}}