import { Flex } from '@DS/core';
import React from 'react';
import KanbanFilterMenu from './KanbanFilterMenu';
import './KanbanBoard.css';

const KanbanBoard: React.FC = () => {
    return (
        <Flex justify="center" className="kanbanBoardContainer">
            <Flex direction="column" align="center" className='kanbanContainer'>
                <KanbanFilterMenu />
                <div>Board</div>
            </Flex>
        </Flex>
    )
}

export default KanbanBoard